# EBP Project Central Registry

A comprehensive web application for managing Evidence-Based Practice (EBP) projects built with Vue 3, PrimeVue, and Firebase.

## Features

### 🏥 Project Management

- **Project Registration**: Complete form with validation for new project registration
- **Project Listing**: Sortable, filterable DataTable with pagination
- **Team Management**: Add/remove collaborators with verification workflow
- **Leadership Transfer**: Secure transfer of project leadership
- **Progress Tracking**: Monitor project status from inception to completion

### 🔐 Security Model

- **Employee Verification**: No Firebase Auth for project edits - uses employee number verification
- **Admin Authentication**: Firebase Auth exclusively for admin panel access
- **Role-based Access**: Public read access, verified write access for projects

### 📱 Responsive Design

- **Mobile-first**: Optimized for all device sizes
- **Breakpoints**: Mobile (<768px), Tablet (768-1024px), Desktop (>1024px)
- **PrimeVue Components**: Fully responsive UI components

### ⚡ Performance

- **Lazy Loading**: Non-essential components loaded on demand
- **Optimized Queries**: Efficient Firestore query patterns
- **State Management**: Centralized state with Pinia

## Tech Stack

- **Frontend**: Vue 3 (Composition API)
- **UI Framework**: PrimeVue with Tailwind CSS
- **State Management**: Pinia
- **Routing**: Vue Router
- **Backend**: Firebase Firestore
- **Authentication**: Firebase Auth (Admin only)
- **Hosting**: Firebase Hosting
- **Build Tool**: Vite

## Project Structure

```
src/
├── components/           # Reusable Vue components
│   ├── NavigationBar.vue
│   ├── CollaboratorManager.vue
│   └── VerificationModal.vue
├── views/               # Page components
│   ├── HomeView.vue
│   ├── ProjectRegisterView.vue
│   ├── ProjectListView.vue
│   ├── AdminView.vue
│   └── LoginView.vue
├── stores/              # Pinia stores
│   ├── counter.js       # Project store (renamed from counter)
│   ├── authStore.js
│   └── configStore.js
├── services/            # API services
│   ├── firebase.js
│   ├── firestoreService.js
│   └── authService.js
├── utils/               # Utility functions
│   ├── constants.js
│   └── validation.js
└── router/              # Vue Router configuration
    └── index.js
```

## Setup Instructions

### 1. Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Firebase account

### 2. Clone and Install

```bash
git clone <repository-url>
cd ebp_cr
npm install
```

### 3. Firebase Configuration

#### Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project
3. Enable Firestore Database
4. Enable Authentication (Email/Password)
5. Enable Hosting

#### Configure Firebase

1. Copy your Firebase config from Project Settings
2. Update `src/services/firebase.js` with your config:

```javascript
const firebaseConfig = {
  apiKey: 'your-api-key',
  authDomain: 'your-project.firebaseapp.com',
  projectId: 'your-project-id',
  storageBucket: 'your-project.appspot.com',
  messagingSenderId: '*********',
  appId: 'your-app-id',
}
```

#### Deploy Firestore Rules

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase in project
firebase init

# Deploy Firestore rules
firebase deploy --only firestore:rules
```

### 4. Development

```bash
# Start development server
npm run dev
```

### 5. Production Build

```bash
# Build for production
npm run build

# Deploy to Firebase Hosting
firebase deploy --only hosting
```

## Configuration

### Default Options

The application comes with predefined options for:

- **Departments**: Emergency Medicine, Internal Medicine, Surgery, etc.
- **Ranks**: Consultant, Senior Registrar, Registrar, etc.
- **Project Types**: EBP, Research, CQI Project, Other
- **Progress Status**: Not Started, In Progress, Completed

These can be customized through the Admin panel.

### Environment Variables

Create a `.env` file for environment-specific configuration:

```env
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
```

## Usage

### For Project Teams

#### Register a New Project

1. Navigate to "Register Project"
2. Fill in all required fields:
   - Department and project details
   - Project leader information
   - Collaborators (optional)
   - Timeline and project type
3. Submit the form

#### Manage Existing Projects

1. Go to "Project List"
2. Use filters to find specific projects
3. Click action buttons to:
   - Edit project details (requires leader verification)
   - Add/remove collaborators
   - Transfer leadership

### For Administrators

#### Access Admin Panel

1. Click "Admin Login" in navigation
2. Sign in with admin credentials
3. Access admin dashboard

#### Manage Configuration

1. Add/remove departments and ranks
2. Export project data as CSV
3. Bulk delete projects
4. Create additional admin users

## Security Features

### Employee Number Verification

- All project modifications require verification
- User must enter the project leader's 6-digit employee number
- System verifies against stored leader data
- Prevents unauthorized changes

### Admin Authentication

- Firebase Authentication for admin access only
- Separate from project verification system
- Role-based access to administrative functions

### Firestore Security Rules

- Public read access for project data
- Write access requires verification workflow
- Admin-only access for configuration and bulk operations

## API Reference

### Project Operations

```javascript
// Create project
await projectService.createProject(projectData)

// Update project
await projectService.updateProject(projectId, updateData)

// Verify project leader
await projectService.verifyProjectLeader(projectId, employeeNumber)

// Add collaborator
await projectService.addCollaborator(projectId, collaboratorData)
```

### Configuration Management

```javascript
// Get configuration
await configService.getConfig()

// Update configuration
await configService.updateConfig(configData)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:

- Create an issue in the repository
- Contact the development team
- Check the documentation

---

Built with ❤️ for Evidence-Based Practice project management.
