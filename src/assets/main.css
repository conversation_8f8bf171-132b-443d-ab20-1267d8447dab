@import './base.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* PrimeVue theme customizations */
:root {
  --primary-color: #3b82f6;
  --primary-color-text: #ffffff;
  --surface-0: #ffffff;
  --surface-50: #f8fafc;
  --surface-100: #f1f5f9;
  --surface-200: #e2e8f0;
  --surface-300: #cbd5e1;
  --surface-400: #94a3b8;
  --surface-500: #64748b;
  --surface-600: #475569;
  --surface-700: #334155;
  --surface-800: #1e293b;
  --surface-900: #0f172a;
}

#app {
  min-height: 100vh;
  font-weight: normal;
}

/* Custom component styles */
.ebp-card {
  @apply bg-white rounded-lg shadow-md border border-gray-200;
}

.ebp-form-field {
  @apply mb-4;
}

.ebp-button-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors;
}

.ebp-button-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors;
}
