<script setup>
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectStore } from '../stores/counter.js'
import { useConfigStore } from '../stores/configStore.js'

const router = useRouter()
const projectStore = useProjectStore()
const configStore = useConfigStore()

const recentProjects = computed(() => {
  return projectStore.projects.slice(0, 5)
})

const stats = computed(() => {
  const total = projectStore.projectCount
  const byStatus = projectStore.projectsByStatus

  return {
    total,
    notStarted: byStatus['not-started']?.length || 0,
    inProgress: byStatus['in-progress']?.length || 0,
    completed: byStatus['completed']?.length || 0,
  }
})

onMounted(() => {
  projectStore.fetchProjects()
})

const navigateToRegister = () => {
  router.push('/register')
}

const navigateToProjects = () => {
  router.push('/projects')
}
</script>

<template>
  <div class="space-y-8">
    <!-- Hero Section -->
    <div
      class="text-center py-12 bg-gradient-to-r from-primary-600 to-primary-800 text-white rounded-lg shadow-lg"
    >
      <h1 class="text-4xl md:text-5xl font-bold mb-4">EBP Project Central Registry</h1>
      <p class="text-xl md:text-2xl mb-8 opacity-90">
        Centralized management for Evidence-Based Practice projects
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <Button
          @click="navigateToRegister"
          label="Register New Project"
          icon="pi pi-plus"
          class="p-button-lg p-button-secondary"
        />
        <Button
          @click="navigateToProjects"
          label="View All Projects"
          icon="pi pi-list"
          class="p-button-lg p-button-outlined"
          style="color: white; border-color: white"
        />
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card class="text-center">
        <template #content>
          <div class="p-4">
            <i class="pi pi-folder text-4xl text-primary-600 mb-3"></i>
            <h3 class="text-2xl font-bold text-gray-800">{{ stats.total }}</h3>
            <p class="text-gray-600">Total Projects</p>
          </div>
        </template>
      </Card>

      <Card class="text-center">
        <template #content>
          <div class="p-4">
            <i class="pi pi-clock text-4xl text-yellow-500 mb-3"></i>
            <h3 class="text-2xl font-bold text-gray-800">{{ stats.notStarted }}</h3>
            <p class="text-gray-600">Not Started</p>
          </div>
        </template>
      </Card>

      <Card class="text-center">
        <template #content>
          <div class="p-4">
            <i class="pi pi-play text-4xl text-blue-500 mb-3"></i>
            <h3 class="text-2xl font-bold text-gray-800">{{ stats.inProgress }}</h3>
            <p class="text-gray-600">In Progress</p>
          </div>
        </template>
      </Card>

      <Card class="text-center">
        <template #content>
          <div class="p-4">
            <i class="pi pi-check-circle text-4xl text-green-500 mb-3"></i>
            <h3 class="text-2xl font-bold text-gray-800">{{ stats.completed }}</h3>
            <p class="text-gray-600">Completed</p>
          </div>
        </template>
      </Card>
    </div>

    <!-- Recent Projects -->
    <Card v-if="recentProjects.length > 0">
      <template #title>
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-bold text-gray-800">Recent Projects</h2>
          <Button
            @click="navigateToProjects"
            label="View All"
            icon="pi pi-arrow-right"
            class="p-button-text"
          />
        </div>
      </template>
      <template #content>
        <div class="space-y-4">
          <div
            v-for="project in recentProjects"
            :key="project.id"
            class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <div class="flex-1">
              <h3 class="font-semibold text-gray-800">{{ project.projectTitle }}</h3>
              <p class="text-sm text-gray-600">{{ project.department }}</p>
              <p class="text-sm text-gray-500">Leader: {{ project.projectLeader?.fullName }}</p>
            </div>
            <div class="text-right">
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="{
                  'bg-yellow-100 text-yellow-800': project.progressStatus === 'not-started',
                  'bg-blue-100 text-blue-800': project.progressStatus === 'in-progress',
                  'bg-green-100 text-green-800': project.progressStatus === 'completed',
                }"
              >
                {{
                  project.progressStatus?.replace('-', ' ').replace(/\b\w/g, (l) => l.toUpperCase())
                }}
              </span>
            </div>
          </div>
        </div>
      </template>
    </Card>

    <!-- Getting Started -->
    <Card v-else>
      <template #title>
        <h2 class="text-2xl font-bold text-gray-800">Getting Started</h2>
      </template>
      <template #content>
        <div class="text-center py-8">
          <i class="pi pi-folder-open text-6xl text-gray-400 mb-4"></i>
          <h3 class="text-xl font-semibold text-gray-700 mb-2">No projects yet</h3>
          <p class="text-gray-600 mb-6">
            Start by registering your first Evidence-Based Practice project.
          </p>
          <Button
            @click="navigateToRegister"
            label="Register First Project"
            icon="pi pi-plus"
            class="p-button-lg"
          />
        </div>
      </template>
    </Card>

    <!-- Features -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card>
        <template #content>
          <div class="p-6 text-center">
            <i class="pi pi-users text-4xl text-primary-600 mb-4"></i>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Collaborative</h3>
            <p class="text-gray-600">
              Manage project teams with multiple collaborators and track leadership changes.
            </p>
          </div>
        </template>
      </Card>

      <Card>
        <template #content>
          <div class="p-6 text-center">
            <i class="pi pi-shield text-4xl text-primary-600 mb-4"></i>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Secure</h3>
            <p class="text-gray-600">
              Employee number verification ensures only authorized personnel can modify projects.
            </p>
          </div>
        </template>
      </Card>

      <Card>
        <template #content>
          <div class="p-6 text-center">
            <i class="pi pi-chart-line text-4xl text-primary-600 mb-4"></i>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Trackable</h3>
            <p class="text-gray-600">
              Monitor project progress from inception to completion with detailed status tracking.
            </p>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>
