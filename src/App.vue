<script setup>
import { RouterView } from 'vue-router'
import { onMounted } from 'vue'
import NavigationBar from './components/NavigationBar.vue'
import { useAuthStore } from './stores/authStore.js'
import { useConfigStore } from './stores/configStore.js'

const authStore = useAuthStore()
const configStore = useConfigStore()

onMounted(async () => {
  // Initialize authentication
  await authStore.initializeAuth()

  // Load configuration
  await configStore.fetchConfig()
})
</script>

<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <NavigationBar />

    <main class="container mx-auto px-4 py-6">
      <RouterView />
    </main>

    <!-- Global Toast component -->
    <Toast />

    <!-- Global Confirmation Dialog -->
    <ConfirmDialog />
  </div>
</template>

<style scoped>
/* Additional global styles can be added here */
</style>
