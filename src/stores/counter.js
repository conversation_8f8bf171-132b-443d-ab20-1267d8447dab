import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { projectService } from '../services/firestoreService.js'

export const useProjectStore = defineStore('project', () => {
  // State
  const projects = ref([])
  const currentProject = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const projectCount = computed(() => projects.value.length)

  const projectsByDepartment = computed(() => {
    const grouped = {}
    projects.value.forEach((project) => {
      const dept = project.department
      if (!grouped[dept]) grouped[dept] = []
      grouped[dept].push(project)
    })
    return grouped
  })

  const projectsByStatus = computed(() => {
    const grouped = {}
    projects.value.forEach((project) => {
      const status = project.progressStatus
      if (!grouped[status]) grouped[status] = []
      grouped[status].push(project)
    })
    return grouped
  })

  // Actions
  const fetchProjects = async () => {
    loading.value = true
    error.value = null

    try {
      const fetchedProjects = await projectService.getAllProjects()
      projects.value = fetchedProjects
    } catch (err) {
      error.value = err.message
      console.error('Error fetching projects:', err)
    } finally {
      loading.value = false
    }
  }

  const createProject = async (projectData) => {
    loading.value = true
    error.value = null

    try {
      const projectId = await projectService.createProject(projectData)
      await fetchProjects() // Refresh the list
      return projectId
    } catch (err) {
      error.value = err.message
      console.error('Error creating project:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateProject = async (projectId, updateData) => {
    loading.value = true
    error.value = null

    try {
      await projectService.updateProject(projectId, updateData)
      await fetchProjects() // Refresh the list
    } catch (err) {
      error.value = err.message
      console.error('Error updating project:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteProject = async (projectId) => {
    loading.value = true
    error.value = null

    try {
      await projectService.deleteProject(projectId)
      await fetchProjects() // Refresh the list
    } catch (err) {
      error.value = err.message
      console.error('Error deleting project:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const verifyProjectLeader = async (projectId, employeeNumber) => {
    try {
      return await projectService.verifyProjectLeader(projectId, employeeNumber)
    } catch (err) {
      error.value = err.message
      console.error('Error verifying project leader:', err)
      throw err
    }
  }

  const addCollaborator = async (projectId, collaboratorData) => {
    loading.value = true
    error.value = null

    try {
      await projectService.addCollaborator(projectId, collaboratorData)
      await fetchProjects() // Refresh the list
    } catch (err) {
      error.value = err.message
      console.error('Error adding collaborator:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const removeCollaborator = async (projectId, collaboratorIndex) => {
    loading.value = true
    error.value = null

    try {
      await projectService.removeCollaborator(projectId, collaboratorIndex)
      await fetchProjects() // Refresh the list
    } catch (err) {
      error.value = err.message
      console.error('Error removing collaborator:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const transferLeadership = async (projectId, newLeaderData) => {
    loading.value = true
    error.value = null

    try {
      await projectService.transferLeadership(projectId, newLeaderData)
      await fetchProjects() // Refresh the list
    } catch (err) {
      error.value = err.message
      console.error('Error transferring leadership:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const bulkDeleteProjects = async (projectIds) => {
    loading.value = true
    error.value = null

    try {
      await projectService.bulkDeleteProjects(projectIds)
      await fetchProjects() // Refresh the list
    } catch (err) {
      error.value = err.message
      console.error('Error bulk deleting projects:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const setCurrentProject = (project) => {
    currentProject.value = project
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    projects,
    currentProject,
    loading,
    error,

    // Getters
    projectCount,
    projectsByDepartment,
    projectsByStatus,

    // Actions
    fetchProjects,
    createProject,
    updateProject,
    deleteProject,
    verifyProjectLeader,
    addCollaborator,
    removeCollaborator,
    transferLeadership,
    bulkDeleteProjects,
    setCurrentProject,
    clearError,
  }
})
